{"template": "shipany-template-one", "theme": "light", "header": {"brand": {"title": "GPT-5 Hub", "logo": {"src": "/logo.png", "alt": "GPT-5 Hub"}, "url": "/"}, "nav": {"items": [{"title": "Prompts", "url": "/#prompts", "icon": "RiSparkling2Line"}, {"title": "Tools", "url": "/#tools", "icon": "RiToolsFill"}, {"title": "GPT-5 vs GPT-4", "url": "/#comparison", "icon": "RiScales3Line"}]}, "buttons": [{"title": "Try GPT-5", "url": "https://chatgpt.com", "target": "_blank", "variant": "link", "icon": "RiArrowRightUpLine"}], "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "Explore the Best GPT-5 Prompts & Tools", "highlight_text": "GPT-5", "description": "Browse high-impact GPT-5 prompts and discover powerful AI tools to write, code, design, and create — faster than ever.", "announcement": {"label": "NEW", "title": "🚀 GPT-5 is now available", "url": "https://chatgpt.com"}, "tip": "💡 Get fresh prompts and tools weekly", "buttons": [{"title": "Browse Prompts", "icon": "RiSparkling2Line", "url": "/#prompts", "target": "_self", "variant": "default"}, {"title": "Explore Tools", "icon": "RiToolsFill", "url": "/#tools", "target": "_self", "variant": "outline"}], "show_happy_users": true, "show_badge": false}, "prompt_categories": {"name": "prompt_categories", "title": "🎯 Featured Prompt Categories", "description": "Discover high-impact GPT-5 prompts organized by category. Copy and use them instantly.", "type": "tabs", "tabs": [{"id": "writing", "title": "Writing & Content", "description": "Blog posts, copywriting, creative writing prompts", "icon": "RiQuillPenLine", "prompts": [{"title": "SEO Blog Post Writer", "content": "Write a comprehensive, SEO-optimized blog post about [TOPIC]. Include: 1) An engaging title with the main keyword, 2) A compelling introduction that hooks the reader, 3) 5-7 main sections with H2 headings, 4) Actionable tips and examples, 5) A conclusion with a clear call-to-action. Target keyword: [KEYWORD]. Word count: 1500-2000 words. Tone: [PROFESSIONAL/CASUAL/EXPERT].", "category": "Content Creation"}, {"title": "Social Media Content Generator", "content": "Create engaging social media content for [PLATFORM] about [TOPIC/PRODUCT]. Include: 1) A hook that stops scrolling, 2) Value-driven content that educates or entertains, 3) A clear call-to-action, 4) Relevant hashtags (5-10), 5) Optimal posting time suggestion. Brand voice: [BRAND_VOICE]. Target audience: [TARGET_AUDIENCE].", "category": "Social Media"}, {"title": "Persuasive Sales Email", "content": "Write a persuasive sales email for [PRODUCT/SERVICE] targeting [TARGET_AUDIENCE]. Structure: 1) Subject line that creates curiosity, 2) Personal greeting, 3) Problem identification, 4) Solution presentation with benefits, 5) Social proof or testimonial, 6) Clear call-to-action, 7) Professional signature. Tone: [CONVERSATIONAL/PROFESSIONAL]. Goal: [SPECIFIC_GOAL].", "category": "Email Marketing"}]}, {"id": "coding", "title": "Coding & Development", "description": "Programming, debugging, code review prompts", "icon": "RiCodeLine", "prompts": [{"title": "Code Debugger & Optimizer", "content": "Debug and optimize this [LANGUAGE] code: [CODE]. Please: 1) Identify all bugs and errors, 2) Explain what each issue does and why it's problematic, 3) Provide the corrected code with comments, 4) Suggest performance optimizations, 5) Recommend best practices for similar code. Focus on: readability, efficiency, and maintainability.", "category": "Debugging"}, {"title": "Component Builder", "content": "Create a [FRAMEWORK] component for [FUNCTIONALITY]. Requirements: 1) Clean, reusable code structure, 2) Proper prop types/interfaces, 3) Error handling, 4) Responsive design considerations, 5) Accessibility features, 6) Unit test examples, 7) Usage documentation with examples. Style: [STYLING_APPROACH]. Additional features: [SPECIFIC_FEATURES].", "category": "Development"}, {"title": "Algorithm Optimizer", "content": "Optimize this algorithm for better performance: [ALGORITHM/CODE]. Please: 1) Analyze current time and space complexity, 2) Identify bottlenecks and inefficiencies, 3) Provide optimized version with explanation, 4) Compare before/after performance, 5) Suggest alternative approaches if applicable, 6) Include test cases to verify correctness.", "category": "Optimization"}]}, {"id": "business", "title": "Business & Strategy", "description": "Business planning, market analysis, strategy prompts", "icon": "RiBarChartBoxLine", "prompts": [{"title": "Market Opportunity Analyzer", "content": "Analyze the market opportunity for [BUSINESS_IDEA/PRODUCT]. Provide: 1) Market size and growth potential, 2) Target customer segments and personas, 3) Competitive landscape analysis, 4) Key market trends and drivers, 5) Barriers to entry and challenges, 6) Revenue model suggestions, 7) Go-to-market strategy recommendations. Industry: [INDUSTRY]. Geographic focus: [LOCATION].", "category": "Market Analysis"}, {"title": "Go-to-Market Strategy", "content": "Create a comprehensive go-to-market strategy for [PRODUCT/SERVICE]. Include: 1) Target market definition and segmentation, 2) Value proposition and positioning, 3) Pricing strategy and model, 4) Distribution channels and partnerships, 5) Marketing and promotion tactics, 6) Sales process and team structure, 7) Success metrics and KPIs, 8) Timeline and milestones. Budget: [BUDGET_RANGE].", "category": "Strategy"}, {"title": "SWOT Analysis Generator", "content": "Generate a detailed SWOT analysis for [COMPANY/PROJECT]. Analyze: 1) Strengths: internal advantages, unique resources, capabilities, 2) Weaknesses: internal limitations, resource gaps, areas for improvement, 3) Opportunities: external factors that could benefit the organization, 4) Threats: external challenges and risks. For each point, provide specific examples and actionable insights. Context: [BUSINESS_CONTEXT].", "category": "Business Analysis"}]}, {"id": "design", "title": "Design & Creative", "description": "UI/UX design, creative concepts, visual ideas", "icon": "RiPaletteLine", "prompts": [{"title": "UI/UX Design Brief", "content": "Design a user-friendly interface for [APP/WEBSITE] targeting [USER_TYPE]. Provide: 1) User journey mapping and key user flows, 2) Information architecture and navigation structure, 3) Wireframe descriptions for key screens, 4) Visual design principles and style guide, 5) Accessibility considerations, 6) Mobile responsiveness approach, 7) Usability testing recommendations. Goals: [SPECIFIC_GOALS].", "category": "UI/UX Design"}, {"title": "Brand Identity Creator", "content": "Create a comprehensive brand identity concept for [BUSINESS/PRODUCT]. Develop: 1) Brand positioning and personality, 2) Target audience and brand voice, 3) Logo concept and visual identity guidelines, 4) Color palette with psychological reasoning, 5) Typography recommendations, 6) Brand messaging and tagline options, 7) Application examples across different media. Industry: [INDUSTRY]. Brand values: [VALUES].", "category": "Branding"}, {"title": "Creative Campaign Generator", "content": "Generate creative campaign ideas for [PRODUCT_LAUNCH/EVENT]. Create: 1) Campaign theme and core message, 2) Creative concepts for different channels (social, digital, print), 3) Visual style and aesthetic direction, 4) Content calendar and timeline, 5) Engagement strategies and interactive elements, 6) Influencer collaboration ideas, 7) Success metrics and measurement plan. Budget: [BUDGET]. Duration: [TIMEFRAME].", "category": "Creative Strategy"}]}, {"id": "data", "title": "Data & Analysis", "description": "Data analysis, research, insights generation", "icon": "RiLineChartLine", "prompts": [{"title": "Data Insights Analyzer", "content": "Analyze this dataset and provide key insights: [DATA/DATASET_DESCRIPTION]. Please: 1) Identify patterns, trends, and anomalies, 2) Provide statistical summary and key metrics, 3) Generate actionable business insights, 4) Suggest data visualization approaches, 5) Recommend next steps for deeper analysis, 6) Highlight potential data quality issues. Focus areas: [SPECIFIC_FOCUS]. Business context: [CONTEXT].", "category": "Data Analysis"}, {"title": "Research Report Generator", "content": "Create a comprehensive research report on [TOPIC]. Structure: 1) Executive summary with key findings, 2) Research methodology and data sources, 3) Detailed analysis with supporting evidence, 4) Key insights and implications, 5) Recommendations and action items, 6) Limitations and areas for further research, 7) Appendices with supporting data. Target audience: [AUDIENCE]. Report length: [LENGTH].", "category": "Research"}, {"title": "Data Visualization Consultant", "content": "Generate data visualization recommendations for [METRICS/DATA_TYPE]. Provide: 1) Best chart types for different data relationships, 2) Dashboard layout and hierarchy suggestions, 3) Color schemes and visual design principles, 4) Interactive elements and user experience, 5) Mobile and accessibility considerations, 6) Storytelling approach with data, 7) Tools and platform recommendations. Audience: [TARGET_AUDIENCE].", "category": "Visualization"}]}, {"id": "education", "title": "Education & Learning", "description": "Teaching, explanations, learning materials", "icon": "RiBookOpenLine", "prompts": [{"title": "Concept Explainer", "content": "Explain [COMPLEX_CONCEPT] in simple terms for [TARGET_AUDIENCE]. Structure: 1) Start with a relatable analogy or real-world example, 2) Break down the concept into digestible parts, 3) Use simple language and avoid jargon, 4) Provide step-by-step explanations, 5) Include practical examples and applications, 6) Add visual description suggestions, 7) End with a summary and key takeaways. Learning level: [BEGINNER/INTERMEDIATE/ADVANCED].", "category": "Teaching"}, {"title": "Study Guide Creator", "content": "Create a comprehensive study guide for [SUBJECT/TOPIC]. Include: 1) Learning objectives and key concepts, 2) Structured content outline with main topics, 3) Important definitions and terminology, 4) Practice questions and exercises, 5) Memory aids and mnemonics, 6) Real-world applications and examples, 7) Additional resources and references, 8) Self-assessment checklist. Target level: [ACADEMIC_LEVEL]. Exam focus: [EXAM_TYPE].", "category": "Study Materials"}, {"title": "Interactive Learning Designer", "content": "Design interactive learning exercises for [SKILL/SUBJECT]. Create: 1) Learning objectives and outcomes, 2) Engaging activities and hands-on exercises, 3) Progressive difficulty levels, 4) Assessment and feedback mechanisms, 5) Gamification elements and rewards, 6) Collaborative learning opportunities, 7) Technology integration suggestions, 8) Adaptation for different learning styles. Duration: [TIME_FRAME]. Group size: [PARTICIPANTS].", "category": "Learning Design"}]}]}, "tools_showcase": {"name": "tools_showcase", "title": "🛠 Top Tools Powered by GPT-5", "description": "Explore a curated directory of AI tools built on or compatible with GPT-5. From code assistants to content generators and visual tools — all in one place.", "tools": [{"id": "scholar-ai", "title": "Scholar AI", "description": "AI-powered research insights.", "chats": "900,000", "image": "https://files.oaiusercontent.com/file-PPto6IuVRN3OQY2WC1gLEI9h?se=2123-12-17T06%3A07%3A04Z&sp=r&sv=2021-08-06&sr=b&rscc=max-age%3D1209600%2C%20immutable&rscd=attachment%3B%20filename%3Dcircle%2520logo.png&sig=uWzfWOVMeiQFanHmBn5O0YLwLx7HuIxHWSCNSo1Qsfc%3D", "url": "/tools/scholar-ai", "category": "Research"}, {"id": "video-gpt-by-veed", "title": "Video GPT by VEED", "description": "AI-Powered Video Creation Simplified", "chats": "900,000", "image": "https://files.oaiusercontent.com/file-4u5wkqX2w4i8SBpkAgthvVXC?se=2124-04-07T20%3A55%3A23Z&sp=r&sv=2021-08-06&sr=b&rscc=max-age%3D1209600%2C%20immutable&rscd=attachment%3B%20filename%3DGPT%2520Store%2520%25282%2529.png&sig=v4ih98prPTbs%2BbJFWJjI2uIadNPos1me4fAuUtiWRTo%3D", "url": "/tools/video-gpt-by-veed", "category": "Video"}, {"id": "wolfram", "title": "<PERSON><PERSON>", "description": "Unlock the Power of AI with <PERSON><PERSON>", "chats": "800,000", "image": "https://files.oaiusercontent.com/file-fGE6EGZCQY73C76MJantfE0d?se=2123-11-20T15%3A51%3A47Z&sp=r&sv=2021-08-06&sr=b&rscc=max-age%3D1209600%2C%20immutable&rscd=attachment%3B%20filename%3DWolfram.png&sig=2QEsZQ5Kxa7XqcJ0uhOOHZMx0qjAhuN2H%2BsZR5p2GXo%3D", "url": "/tools/wolfram", "category": "Math & Science"}, {"id": "webpilot", "title": "WebPilot", "description": "AI-driven web analysis and content creation", "chats": "800,000", "image": "https://files.oaiusercontent.com/file-WCpXavrnVzievanNCKCQBSHC?se=2123-12-25T04%3A39%3A21Z&sp=r&sv=2021-08-06&sr=b&rscc=max-age%3D1209600%2C%20immutable&rscd=attachment%3B%20filename%3DWebPilot_Logo.jpg&sig=srB2//6MMMj0T%2BNRuDljzYjCsYYMngBRcaSN%2BpX6tIs%3D", "url": "/tools/webpilot", "category": "Web Analysis"}, {"id": "photo-multiverse", "title": "Photo Multiverse", "description": "Transform your photos with AI magic!", "chats": "500,000", "image": "https://files.oaiusercontent.com/file-gT39Cd6TylxqOTQZaFSRY4oP?se=2123-12-30T01%3A23%3A43Z&sp=r&sv=2021-08-06&sr=b&rscc=max-age%3D1209600%2C%20immutable&rscd=attachment%3B%20filename%3D63e266a6-3c34-4db2-a563-724dfc46d09d.png&sig=wjyrzBu6J1sumJBTD6pyBmFOwogoHJ46hB17Kmor/QA%3D", "url": "/tools/photo-multiverse", "category": "Photo Editing"}, {"id": "whimsical-diagrams", "title": "Whimsical Diagrams", "description": "Visualize your ideas with AI-powered precision.", "chats": "500,000", "image": "https://files.oaiusercontent.com/file-w1wRg58ZPtZgXXTQJqiEwJ4H?se=2124-01-06T16%3A22%3A56Z&sp=r&sv=2021-08-06&sr=b&rscc=max-age%3D1209600%2C%20immutable&rscd=attachment%3B%20filename%3DWhite%2520Icon%2520on%2520Gradient%2520SQ%25402x.png&sig=9eaJvzM4CZfWuo08Mik5OfaxwqRbEfZl0J333af9dRo%3D", "url": "/tools/whimsical-diagrams", "category": "Diagrams"}, {"id": "super-describe", "title": "Super Describe", "description": "AI-Powered Image Analysis and Recreation", "chats": "500,000", "image": "https://files.oaiusercontent.com/file-wSYkolaw1qEQ3s5gA0Z9i0x8?se=2123-10-20T17%3A29%3A43Z&sp=r&sv=2021-08-06&sr=b&rscc=max-age%3D31536000%2C%20immutable&rscd=attachment%3B%20filename%3D5bb5263b-c53b-4ac2-8bf5-0d4d5eb2f246.png&sig=2u8YEYxkfBBfVNFE2qicuaNpJkyCf9J7tqMzhHZKHHE%3D", "url": "/tools/super-describe", "category": "Image Analysis"}, {"id": "math", "title": "math", "description": "AI-powered math solutions for everyone", "chats": "500,000", "image": "https://files.oaiusercontent.com/file-Yfha57T7F38FgiPyfAoMh5LY?se=2124-02-06T02%3A35%3A58Z&sp=r&sv=2021-08-06&sr=b&rscc=max-age%3D1209600%2C%20immutable&rscd=attachment%3B%20filename%3Dgpt11.png&sig=VGYkuNCr4260k6sWG5qy7gcgMnYNpHMNyAZ6MESt7OI%3D", "url": "/tools/math", "category": "Math"}, {"id": "video-maker", "title": "Video Maker", "description": "AI-powered Video Creation Simplified", "chats": "500,000", "image": "https://files.oaiusercontent.com/file-Y5VfZYtGbbkXhPTzqGn8mhGW?se=2123-12-24T13%3A42%3A16Z&sp=r&sv=2021-08-06&sr=b&rscc=max-age%3D1209600%2C%20immutable&rscd=attachment%3B%20filename%3DVideoMaker-blue%25402x.png&sig=m4ZiA/1bZnp56z2D3IlEnQkwD3NlK2E333ss%2B%2B7Y16w%3D", "url": "/tools/video-maker", "category": "Video"}, {"id": "video-tutor", "title": "Video Tutor✏️🌐🎓", "description": "AI-Powered Insights for Video Content", "chats": "400,000", "image": "https://files.oaiusercontent.com/file-XQjlWvKvgBqpv1CrBpv7phuW?se=2124-04-28T16%3A27%3A25Z&sp=r&sv=2023-11-03&sr=b&rscc=max-age%3D1209600%2C%20immutable&rscd=attachment%3B%20filename%3DVideo%2520Tutor.png&sig=J6J9BUtIiv2HJqlFh/sGSVVSnGWKOM65/d3w8e2zsmI%3D", "url": "/tools/video-tutor", "category": "Education"}, {"id": "logo", "title": "LOGO", "description": "AI-powered harmony in design.", "chats": "400,000", "image": "https://files.oaiusercontent.com/file-lvZdHWZGgQwvsX1a7z6jDqjp?se=2123-10-24T06%3A40%3A10Z&sp=r&sv=2021-08-06&sr=b&rscc=max-age%3D31536000%2C%20immutable&rscd=attachment%3B%20filename%3D001.jpg&sig=TiSo//G1KZ6wuBgQEpI2kzsvwxZ3R8HRIWZEFGabBi4%3D", "url": "/tools/logo", "category": "Design"}, {"id": "slide-maker", "title": "Slide Maker: PowerPoints, Presentations", "description": "AI-Powered Presentation Creation", "chats": "400,000", "image": "https://files.oaiusercontent.com/file-7wucSlKrcCoipKRoTaxydYsv?se=2123-10-18T01%3A57%3A11Z&sp=r&sv=2021-08-06&sr=b&rscc=max-age%3D31536000%2C%20immutable&rscd=attachment%3B%20filename%3Dlogo.png&sig=b8skgReALrhw4frzasMMf/5yY148%2B5pjmi9YtngLHkA%3D", "url": "/tools/slide-maker", "category": "Presentations"}, {"id": "universal-primer", "title": "Universal Primer", "description": "AI-powered clarity for complex subjects.", "chats": "400,000", "image": "https://files.oaiusercontent.com/file-thqJUpDWcYAMxgKhiwNYZFj0?se=2123-10-17T10%3A02%3A59Z&sp=r&sv=2021-08-06&sr=b&rscc=max-age%3D31536000%2C%20immutable&rscd=attachment%3B%20filename%3D1fb91f30-1519-4f62-81f7-e141fa19f099.png&sig=UljckMV40AarnEvasy2Z2yiDGjVV1UFD/e0dtZkyETI%3D", "url": "/tools/universal-primer", "category": "Education"}, {"id": "photo-realistic-gpt", "title": "Photo Realistic GPT", "description": "Transforming Text into Realistic Images with AI", "chats": "400,000", "image": "https://files.oaiusercontent.com/file-GyjjbdHxR7sQT8c2ZfIMAIfw?se=2124-02-24T22%3A31%3A05Z&sp=r&sv=2021-08-06&sr=b&rscc=max-age%3D1209600%2C%20immutable&rscd=attachment%3B%20filename%3DIMG_7599.png&sig=He2JdpcLDBmkrJPeZQAeOnOQDgCPSnP2fmGpsRxW1eI%3D", "url": "/tools/photo-realistic-gpt", "category": "Image Generation"}, {"id": "ai-humanizer-pro", "title": "AI Humanizer Pro", "description": "Humanize your AI-generated text effortlessly.", "chats": "300,000", "image": "https://files.oaiusercontent.com/file-9P5nlYHHRNrE0IFT1WqMEHfB?se=2123-12-31T09%3A45%3A35Z&sp=r&sv=2021-08-06&sr=b&rscc=max-age%3D1209600%2C%20immutable&rscd=attachment%3B%20filename%3DBypass%2520GPT.png&sig=UduBsGC57CSjWsMA%2BEPuf7jYougMcNhQEveOnchw%2B2k%3D", "url": "/tools/ai-humanizer-pro", "category": "Text Processing"}]}, "comparison": {"name": "comparison", "title": "⚖️ GPT-5 vs GPT-4: What's New?", "label": "Comparison", "description": "Discover the key improvements and new capabilities that make GPT-5 a significant upgrade over GPT-4.", "comparison_table": {"headers": ["Feature", "GPT-4", "GPT-5"], "rows": [{"feature": "<PERSON> Context", "gpt4": "128k", "gpt5": "Up to 1M (expected)"}, {"feature": "Speed & Latency", "gpt4": "Medium", "gpt5": "Faster"}, {"feature": "Reasoning Power", "gpt4": "High", "gpt5": "Very High"}, {"feature": "Multimodal Input", "gpt4": "Text + Image", "gpt5": "Text + Image + Video + Audio"}, {"feature": "Agentic Abilities", "gpt4": "Limited", "gpt5": "Expanded (multi-step tasks)"}, {"feature": "Tool Use", "gpt4": "Manual", "gpt5": "More native integration"}]}, "items": [{"title": "Enhanced Context Window", "description": "GPT-5 can process up to 1M tokens, allowing for much longer conversations and document analysis.", "icon": "RiFileTextLine", "image": {"src": "/imgs/features/2.png"}}, {"title": "Multimodal Capabilities", "description": "Native support for text, images, video, and audio input makes GPT-5 truly multimodal.", "icon": "RiImageLine", "image": {"src": "/imgs/features/3.png"}}, {"title": "Advanced Reasoning", "description": "Improved logical reasoning and problem-solving capabilities for complex tasks and analysis.", "icon": "RiBrainLine", "image": {"src": "/imgs/features/4.png"}}]}, "feature": {"name": "feature", "title": "Why GPT-5 Hub is Your Go-To Resource", "description": "Everything you need to maximize your GPT-5 experience and productivity.", "items": [{"title": "Curated Prompt Library", "description": "Hand-picked, tested prompts across 6+ categories with copy-to-clipboard functionality.", "icon": "RiSparkling2Line"}, {"title": "Tool Directory", "description": "Comprehensive directory of GPT-5 compatible tools with ratings and reviews.", "icon": "RiToolsFill"}, {"title": "Weekly Updates", "description": "Fresh prompts, new tools, and creator tricks delivered to your inbox every week.", "icon": "RiCalendarLine"}, {"title": "Copy & Use Instantly", "description": "One-click copy functionality for all prompts - no signup required to browse.", "icon": "RiFileCopyLine"}, {"title": "Expert Curation", "description": "All content is tested and curated by AI experts and power users.", "icon": "RiStarLine"}, {"title": "Community Driven", "description": "Submit your own prompts and tools to help the community grow.", "icon": "RiTeamLine"}]}, "stats": {"name": "stats", "label": "Stats", "title": "GPT-5 Hub by the Numbers", "description": "Join thousands of users maximizing their GPT-5 potential.", "icon": "RiBarChartBoxLine", "items": [{"title": "Curated", "label": "500+", "description": "Prompts"}, {"title": "Featured", "label": "50+", "description": "Tools"}, {"title": "Weekly", "label": "10K+", "description": "Users"}]}, "testimonial": {"name": "testimonial", "label": "Testimonial", "title": "What GPT-5 Power Users Say", "description": "Hear from creators, developers, and professionals who use GPT-5 Hub daily.", "icon": "GoThumbsup", "items": [{"title": "<PERSON>", "label": "Content Creator", "description": "GPT-5 <PERSON><PERSON>'s prompts have transformed my content workflow. I can create weeks of content in hours now. The writing prompts are pure gold!", "image": {"src": "/imgs/users/1.png"}}, {"title": "<PERSON>", "label": "Software Developer", "description": "The coding prompts are incredibly detailed and practical. I've improved my debugging speed by 3x using the prompts from this site.", "image": {"src": "/imgs/users/2.png"}}, {"title": "<PERSON>", "label": "Marketing Manager", "description": "Finally, a resource that actually understands how to use GPT-5 effectively. The business strategy prompts have helped me create better campaigns.", "image": {"src": "/imgs/users/3.png"}}, {"title": "<PERSON>", "label": "UX Designer", "description": "The design prompts are spot-on. GPT-5 <PERSON><PERSON> helped me discover new ways to use AI in my design process. Highly recommend!", "image": {"src": "/imgs/users/4.png"}}, {"title": "<PERSON>", "label": "Data Analyst", "description": "The data analysis prompts are incredibly sophisticated. I can generate insights and reports that would take me days in just minutes.", "image": {"src": "/imgs/users/5.png"}}, {"title": "<PERSON>", "label": "Educator", "description": "As a teacher, the education prompts have revolutionized how I create learning materials. My students are more engaged than ever!", "image": {"src": "/imgs/users/6.png"}}]}, "faq": {"name": "faq", "label": "FAQ", "title": "GPT-5: Common Questions Answered", "description": "Have another question? Join our community or contact us.", "items": [{"title": "Where can I try GPT-5?", "description": "Right now on ChatGPT (Plus users). Click here to try: https://chatgpt.com"}, {"title": "What makes GPT-5 better than GPT-4?", "description": "Bigger context (up to 1M tokens), faster processing, smarter reasoning, and enhanced multimodal capabilities including video and audio input."}, {"title": "Can GPT-5 generate images, video or sound?", "description": "Not natively — but you can pair GPT-5 with tools like Midjourney, Runway, and ElevenLabs for that. Our tools directory shows you exactly how."}, {"title": "Is GPT-5 free to use?", "description": "It requires ChatGPT Plus, currently $20/mo. Some features may have additional usage limits."}, {"title": "Can I build tools with GPT-5 API?", "description": "API access may be available soon. Stay tuned for updates from OpenAI."}, {"title": "How do I get the most out of GPT-5?", "description": "Use our curated prompts, explore compatible tools, and join our weekly newsletter for the latest tips and tricks from power users."}]}, "cta": {"name": "cta", "title": "📬 Join the Prompt List", "description": "Get fresh GPT-5 prompts, new tools, and creator tricks — in your inbox every week.", "buttons": [{"title": "Join <PERSON> →", "url": "#newsletter", "target": "_self", "icon": "RiMailLine"}, {"title": "View All FAQs →", "url": "/#faq", "target": "_self", "variant": "outline"}]}, "footer": {"name": "footer", "brand": {"title": "GPT-5 Hub", "description": "Your ultimate resource for GPT-5 prompts, tools, and insights. Discover the best ways to leverage GPT-5 for writing, coding, design, and more.", "logo": {"src": "/logo.png", "alt": "GPT-5 Hub"}, "url": "/"}, "copyright": "© 2025 • GPT-5 Hub All rights reserved.", "nav": {"items": [{"title": "Browse", "children": [{"title": "Prompts", "url": "/#prompts", "target": "_self"}, {"title": "Tools", "url": "/#tools", "target": "_self"}, {"title": "Comparison", "url": "/#comparison", "target": "_self"}]}, {"title": "Resources", "children": [{"title": "GPT-5 Guide", "url": "/guide", "target": "_self"}, {"title": "Best Practices", "url": "/best-practices", "target": "_self"}, {"title": "Newsletter", "url": "#newsletter", "target": "_self"}]}, {"title": "Community", "children": [{"title": "Submit Prompt", "url": "/submit", "target": "_self"}, {"title": "Submit Tool", "url": "/submit-tool", "target": "_self"}, {"title": "Join <PERSON>", "url": "https://discord.gg/gpt5hub", "target": "_blank"}]}]}, "social": {"items": [{"title": "X", "icon": "RiTwitterXFill", "url": "https://x.com/gpt5hub", "target": "_blank"}, {"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "https://github.com/gpt5hub", "target": "_blank"}, {"title": "Discord", "icon": "RiDiscordFill", "url": "https://discord.gg/gpt5hub", "target": "_blank"}, {"title": "Email", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_self"}]}, "agreement": {"items": [{"title": "Privacy Policy", "url": "/privacy-policy"}, {"title": "Terms of Service", "url": "/terms-of-service"}]}}}