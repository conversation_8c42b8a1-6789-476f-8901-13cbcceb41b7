import {
  getMessages,
  getTranslations,
  setRequestLocale,
} from "next-intl/server";
import { AppContextProvider } from "@/contexts/app";
import { Metadata } from "next";
import { NextAuthSessionProvider } from "@/auth/session";
import { NextIntlClientProvider } from "next-intl";
import { ThemeProvider } from "@/providers/theme";
import { generateSEOMetadata, getPageKeywords } from "@/lib/seo";
import { SEO_CONFIG } from "@/lib/seo-config";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  setRequestLocale(locale);

  const t = await getTranslations();
  const baseUrl = SEO_CONFIG.siteUrl;
  const canonicalUrl = locale === 'en' ? baseUrl : `${baseUrl}/${locale}`;

  return generateSEOMetadata({
    title: t("metadata.title") || SEO_CONFIG.defaultTitle[locale as keyof typeof SEO_CONFIG.defaultTitle] || SEO_CONFIG.defaultTitle.en,
    description: t("metadata.description") || SEO_CONFIG.defaultDescription[locale as keyof typeof SEO_CONFIG.defaultDescription] || SEO_CONFIG.defaultDescription.en,
    keywords: t("metadata.keywords") || getPageKeywords('home', locale),
    url: locale === 'en' ? '/' : `/${locale}`,
    locale,
    type: 'website',
  });
}

export default async function LocaleLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}>) {
  const { locale } = await params;
  setRequestLocale(locale);

  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages}>
      <NextAuthSessionProvider>
        <AppContextProvider>
          <ThemeProvider attribute="class" disableTransitionOnChange>
            {children}
          </ThemeProvider>
        </AppContextProvider>
      </NextAuthSessionProvider>
    </NextIntlClientProvider>
  );
}
