// SEO Configuration for GPT-5 Hub
export const SEO_CONFIG = {
  // Basic site information
  siteName: 'GPT-5 Hub',
  siteUrl: process.env.NEXT_PUBLIC_WEB_URL || 'https://gpt-5.host',
  defaultLocale: 'en',
  supportedLocales: ['en', 'zh'],
  
  // Default meta information
  defaultTitle: {
    en: 'GPT-5 Hub - Best GPT-5 Prompts, Tools & AI Resources',
    zh: 'GPT-5 中文站 - 最全GPT-5提示词库和AI工具集'
  },
  
  defaultDescription: {
    en: 'Discover the ultimate collection of GPT-5 prompts, AI tools, and resources. Master GPT-5 for writing, coding, design, and business automation.',
    zh: 'GPT-5中文资源站，提供最全面的GPT-5提示词库、AI工具目录和使用教程。掌握GPT-5写作、编程、设计技巧，提升AI生产力。'
  },
  
  // Social media handles
  social: {
    twitter: '@gpt5hub',
    github: 'gpt5hub',
    discord: 'gpt5hub',
  },
  
  // OpenGraph default image
  defaultImage: '/logo.png',
  
  // Structured data organization info
  organization: {
    name: 'GPT-5 Hub',
    url: process.env.NEXT_PUBLIC_WEB_URL || 'https://gpt-5.host',
    logo: `${process.env.NEXT_PUBLIC_WEB_URL || 'https://gpt-5.host'}/logo.png`,
    description: 'Ultimate resource for GPT-5 prompts, tools, and AI insights',
    foundingDate: '2024',
    contactPoint: {
      contactType: 'customer service',
      email: '<EMAIL>',
    },
  },
  
  // Page-specific SEO configurations
  pages: {
    home: {
      priority: 1.0,
      changefreq: 'daily',
      keywords: {
        en: 'GPT-5, GPT-5 prompts, GPT-5 tools, AI prompts, ChatGPT-5, OpenAI GPT-5, AI writing tools, GPT-5 vs GPT-4, artificial intelligence, AI automation, prompt engineering, GPT-5 examples, AI productivity tools, machine learning prompts',
        zh: 'GPT-5, GPT-5提示词, GPT-5中文, AI提示词, ChatGPT-5, OpenAI GPT-5, AI写作工具, GPT-5对比GPT-4, 人工智能, AI自动化, 提示词工程, GPT-5案例, AI生产力工具, 机器学习提示词'
      }
    },
    prompts: {
      priority: 0.9,
      changefreq: 'daily',
      keywords: {
        en: 'GPT-5 prompts, AI prompts, prompt engineering, GPT-5 examples, writing prompts, coding prompts, creative prompts, business prompts, marketing prompts, SEO prompts, content creation prompts',
        zh: 'GPT-5提示词, AI提示词, 提示词工程, GPT-5案例, 写作提示词, 编程提示词, 创意提示词, 商业提示词, 营销提示词, SEO提示词, 内容创作提示词'
      }
    },
    tools: {
      priority: 0.9,
      changefreq: 'weekly',
      keywords: {
        en: 'GPT-5 tools, AI tools, productivity tools, writing tools, coding tools, design tools, automation tools, AI software, GPT-5 applications, AI utilities',
        zh: 'GPT-5工具, AI工具, 生产力工具, 写作工具, 编程工具, 设计工具, 自动化工具, AI软件, GPT-5应用, AI实用工具'
      }
    },
    comparison: {
      priority: 0.8,
      changefreq: 'monthly',
      keywords: {
        en: 'GPT-5 vs GPT-4, AI model comparison, GPT comparison, OpenAI models, AI capabilities, language model comparison, GPT-5 features, GPT-4 features, AI performance',
        zh: 'GPT-5对比GPT-4, AI模型比较, GPT比较, OpenAI模型, AI能力, 语言模型比较, GPT-5功能, GPT-4功能, AI性能'
      }
    },
    blog: {
      priority: 0.7,
      changefreq: 'daily',
      keywords: {
        en: 'GPT-5 news, AI news, GPT-5 tutorial, AI tutorial, prompt engineering guide, AI insights, machine learning news, OpenAI updates, AI trends, GPT-5 tips',
        zh: 'GPT-5新闻, AI新闻, GPT-5教程, AI教程, 提示词工程指南, AI见解, 机器学习新闻, OpenAI更新, AI趋势, GPT-5技巧'
      }
    },
    tutorial: {
      priority: 0.7,
      changefreq: 'weekly',
      keywords: {
        en: 'GPT-5 tutorial, AI tutorial, prompt engineering guide, GPT-5 guide, AI learning, machine learning tutorial, GPT-5 tips, AI best practices',
        zh: 'GPT-5教程, AI教程, 提示词工程指南, GPT-5指南, AI学习, 机器学习教程, GPT-5技巧, AI最佳实践'
      }
    },
    examples: {
      priority: 0.6,
      changefreq: 'weekly',
      keywords: {
        en: 'GPT-5 examples, AI examples, prompt examples, GPT-5 use cases, AI applications, real-world examples, GPT-5 demos, AI showcase',
        zh: 'GPT-5示例, AI示例, 提示词示例, GPT-5用例, AI应用, 实际案例, GPT-5演示, AI展示'
      }
    }
  },
  
  // Technical SEO settings
  technical: {
    // Robots meta tag settings
    robots: {
      index: true,
      follow: true,
      noarchive: false,
      nosnippet: false,
      noimageindex: false,
      nocache: false,
    },
    
    // Googlebot specific settings
    googlebot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
    
    // Verification codes (to be set in environment variables)
    verification: {
      google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,
      bing: process.env.NEXT_PUBLIC_BING_SITE_VERIFICATION,
      yandex: process.env.NEXT_PUBLIC_YANDEX_SITE_VERIFICATION,
      baidu: process.env.NEXT_PUBLIC_BAIDU_SITE_VERIFICATION,
    },
    
    // Analytics and tracking
    analytics: {
      googleAnalytics: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
      googleTagManager: process.env.NEXT_PUBLIC_GTM_ID,
      baiduAnalytics: process.env.NEXT_PUBLIC_BAIDU_ANALYTICS_ID,
    },
  },
  
  // Content guidelines for SEO
  content: {
    titleLength: {
      min: 30,
      max: 60,
      optimal: 55
    },
    descriptionLength: {
      min: 120,
      max: 160,
      optimal: 155
    },
    keywordDensity: {
      min: 0.5,
      max: 2.5,
      optimal: 1.5
    }
  },
  
  // Hreflang configuration
  hreflang: {
    'en': '',
    'zh': '/zh',
    'x-default': ''
  },
  
  // Sitemap configuration
  sitemap: {
    changefreq: {
      home: 'daily',
      prompts: 'daily',
      tools: 'weekly',
      blog: 'daily',
      comparison: 'monthly',
      tutorial: 'weekly',
      examples: 'weekly',
      static: 'monthly'
    },
    priority: {
      home: 1.0,
      prompts: 0.9,
      tools: 0.9,
      blog: 0.7,
      comparison: 0.8,
      tutorial: 0.7,
      examples: 0.6,
      static: 0.5
    }
  }
};

// Helper functions for SEO
export function getPageSEOConfig(page: string, locale: string = 'en') {
  const pageConfig = SEO_CONFIG.pages[page as keyof typeof SEO_CONFIG.pages];
  if (!pageConfig) return null;
  
  return {
    ...pageConfig,
    keywords: pageConfig.keywords[locale as keyof typeof pageConfig.keywords] || pageConfig.keywords.en,
    title: SEO_CONFIG.defaultTitle[locale as keyof typeof SEO_CONFIG.defaultTitle] || SEO_CONFIG.defaultTitle.en,
    description: SEO_CONFIG.defaultDescription[locale as keyof typeof SEO_CONFIG.defaultDescription] || SEO_CONFIG.defaultDescription.en,
  };
}

export function generateCanonicalUrl(path: string, locale: string = 'en') {
  const baseUrl = SEO_CONFIG.siteUrl;
  const localePath = locale === 'en' ? '' : `/${locale}`;
  return `${baseUrl}${localePath}${path}`;
}

export function generateAlternateUrls(path: string) {
  const baseUrl = SEO_CONFIG.siteUrl;
  return SEO_CONFIG.supportedLocales.reduce((acc, locale) => {
    const localePath = locale === 'en' ? '' : `/${locale}`;
    acc[locale] = `${baseUrl}${localePath}${path}`;
    return acc;
  }, {} as Record<string, string>);
}
