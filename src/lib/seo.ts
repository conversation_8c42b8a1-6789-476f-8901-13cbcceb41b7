import { Metadata } from 'next';

export interface SEOConfig {
  title: string;
  description: string;
  keywords: string;
  url: string;
  image?: string;
  locale: string;
  type?: 'website' | 'article';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
}

export function generateSEOMetadata(config: SEOConfig): Metadata {
  const {
    title,
    description,
    keywords,
    url,
    image = '/logo.png',
    locale,
    type = 'website',
    author,
    publishedTime,
    modifiedTime,
  } = config;

  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://gpt-5.host';
  const fullUrl = `${baseUrl}${url}`;
  const fullImageUrl = image.startsWith('http') ? image : `${baseUrl}${image}`;

  return {
    title,
    description,
    keywords,
    authors: author ? [{ name: author }] : undefined,
    creator: 'GPT-5 Hub',
    publisher: 'GPT-5 Hub',
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      type,
      locale,
      url: fullUrl,
      title,
      description,
      siteName: 'GPT-5 Hub',
      images: [
        {
          url: fullImageUrl,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [fullImageUrl],
      creator: '@gpt5hub',
      site: '@gpt5hub',
    },
    alternates: {
      canonical: fullUrl,
      languages: {
        'en': `${baseUrl}${url}`,
        'zh': `${baseUrl}/zh${url}`,
        'x-default': `${baseUrl}${url}`,
      },
    },
    other: {
      ...(publishedTime && { 'article:published_time': publishedTime }),
      ...(modifiedTime && { 'article:modified_time': modifiedTime }),
    },
  };
}

export function generateStructuredData(config: SEOConfig) {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://gpt-5.host';
  
  const organizationSchema = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'GPT-5 Hub',
    url: baseUrl,
    logo: `${baseUrl}/logo.png`,
    description: 'Ultimate resource for GPT-5 prompts, tools, and insights',
    sameAs: [
      'https://twitter.com/gpt5hub',
      'https://github.com/gpt5hub',
    ],
  };

  const websiteSchema = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'GPT-5 Hub',
    url: baseUrl,
    description: config.description,
    potentialAction: {
      '@type': 'SearchAction',
      target: `${baseUrl}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
  };

  const breadcrumbSchema = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'Home',
        item: baseUrl,
      },
    ],
  };

  return {
    organization: organizationSchema,
    website: websiteSchema,
    breadcrumb: breadcrumbSchema,
  };
}

// GPT-5 specific keywords for different pages
export const GPT5_KEYWORDS = {
  home: [
    'GPT-5', 'GPT-5 prompts', 'GPT-5 tools', 'AI prompts', 'ChatGPT-5',
    'OpenAI GPT-5', 'AI writing tools', 'GPT-5 vs GPT-4', 'artificial intelligence',
    'AI automation', 'prompt engineering', 'GPT-5 examples', 'AI productivity tools',
    'machine learning prompts', 'GPT-5 tutorial', 'AI assistant', 'natural language processing'
  ],
  prompts: [
    'GPT-5 prompts', 'AI prompts', 'prompt engineering', 'GPT-5 examples',
    'writing prompts', 'coding prompts', 'creative prompts', 'business prompts',
    'marketing prompts', 'SEO prompts', 'content creation prompts'
  ],
  tools: [
    'GPT-5 tools', 'AI tools', 'productivity tools', 'writing tools',
    'coding tools', 'design tools', 'automation tools', 'AI software',
    'GPT-5 applications', 'AI utilities'
  ],
  blog: [
    'GPT-5 news', 'AI news', 'GPT-5 tutorial', 'AI tutorial',
    'prompt engineering guide', 'AI insights', 'machine learning news',
    'OpenAI updates', 'AI trends', 'GPT-5 tips'
  ],
  comparison: [
    'GPT-5 vs GPT-4', 'AI model comparison', 'GPT comparison',
    'OpenAI models', 'AI capabilities', 'language model comparison',
    'GPT-5 features', 'GPT-4 features', 'AI performance'
  ]
};

export function getPageKeywords(page: keyof typeof GPT5_KEYWORDS, locale: string = 'en'): string {
  const keywords = GPT5_KEYWORDS[page] || GPT5_KEYWORDS.home;
  
  if (locale === 'zh') {
    const chineseKeywords = [
      'GPT-5', 'GPT-5提示词', 'GPT-5工具', 'AI提示词', 'ChatGPT-5',
      'OpenAI GPT-5', 'AI写作工具', 'GPT-5对比GPT-4', '人工智能',
      'AI自动化', '提示词工程', 'GPT-5案例', 'AI生产力工具',
      '机器学习提示词', 'GPT-5教程', 'AI助手', '自然语言处理'
    ];
    return [...keywords, ...chineseKeywords].join(', ');
  }
  
  return keywords.join(', ');
}
