import Head from 'next/head';
import { generateSEOMetadata, getPageKeywords } from '@/lib/seo';
import { StructuredData } from './structured-data';

interface SEOHeadProps {
  title: string;
  description: string;
  url: string;
  locale: string;
  image?: string;
  type?: 'website' | 'article' | 'product';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  pageType?: 'home' | 'prompts' | 'tools' | 'blog' | 'comparison';
  structuredData?: Record<string, any> | Record<string, any>[];
}

export function SEOHead({
  title,
  description,
  url,
  locale,
  image,
  type = 'website',
  author,
  publishedTime,
  modifiedTime,
  pageType = 'home',
  structuredData,
}: SEOHeadProps) {
  const keywords = getPageKeywords(pageType, locale);
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://gpt-5.host';
  const fullUrl = `${baseUrl}${url}`;
  const fullImageUrl = image ? (image.startsWith('http') ? image : `${baseUrl}${image}`) : `${baseUrl}/logo.png`;

  // Generate additional meta tags for GPT-5 specific content
  const gpt5MetaTags = {
    'application-name': 'GPT-5 Hub',
    'apple-mobile-web-app-title': 'GPT-5 Hub',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'mobile-web-app-capable': 'yes',
    'msapplication-TileColor': '#000000',
    'msapplication-config': '/browserconfig.xml',
    'theme-color': '#000000',
  };

  return (
    <>
      <Head>
        {/* Basic Meta Tags */}
        <title>{title}</title>
        <meta name="description" content={description} />
        <meta name="keywords" content={keywords} />
        <meta name="author" content={author || 'GPT-5 Hub Team'} />
        <meta name="creator" content="GPT-5 Hub" />
        <meta name="publisher" content="GPT-5 Hub" />
        <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
        <meta name="googlebot" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
        <meta name="bingbot" content="index, follow" />
        
        {/* Language and Locale */}
        <meta httpEquiv="content-language" content={locale} />
        <meta name="language" content={locale} />
        
        {/* Open Graph Meta Tags */}
        <meta property="og:type" content={type} />
        <meta property="og:title" content={title} />
        <meta property="og:description" content={description} />
        <meta property="og:url" content={fullUrl} />
        <meta property="og:site_name" content="GPT-5 Hub" />
        <meta property="og:image" content={fullImageUrl} />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <meta property="og:image:alt" content={title} />
        <meta property="og:locale" content={locale === 'zh' ? 'zh_CN' : 'en_US'} />
        
        {/* Twitter Card Meta Tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@gpt5hub" />
        <meta name="twitter:creator" content="@gpt5hub" />
        <meta name="twitter:title" content={title} />
        <meta name="twitter:description" content={description} />
        <meta name="twitter:image" content={fullImageUrl} />
        <meta name="twitter:image:alt" content={title} />
        
        {/* Article specific meta tags */}
        {type === 'article' && publishedTime && (
          <meta property="article:published_time" content={publishedTime} />
        )}
        {type === 'article' && modifiedTime && (
          <meta property="article:modified_time" content={modifiedTime} />
        )}
        {type === 'article' && author && (
          <meta property="article:author" content={author} />
        )}
        
        {/* Additional GPT-5 specific meta tags */}
        {Object.entries(gpt5MetaTags).map(([name, content]) => (
          <meta key={name} name={name} content={content} />
        ))}
        
        {/* Canonical URL */}
        <link rel="canonical" href={fullUrl} />
        
        {/* Alternate language versions */}
        <link rel="alternate" hrefLang="en" href={`${baseUrl}${url}`} />
        <link rel="alternate" hrefLang="zh" href={`${baseUrl}/zh${url}`} />
        <link rel="alternate" hrefLang="x-default" href={`${baseUrl}${url}`} />
        
        {/* Preload critical resources */}
        <link rel="preload" href="/logo.png" as="image" />
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//www.google-analytics.com" />
        <link rel="dns-prefetch" href="//api.openai.com" />
        
        {/* Schema.org structured data for GPT-5 content */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'WebPage',
              name: title,
              description: description,
              url: fullUrl,
              image: fullImageUrl,
              inLanguage: locale,
              isPartOf: {
                '@type': 'WebSite',
                name: 'GPT-5 Hub',
                url: baseUrl,
              },
              about: {
                '@type': 'Thing',
                name: 'GPT-5',
                description: 'Advanced AI language model for various applications',
              },
              keywords: keywords,
              datePublished: publishedTime,
              dateModified: modifiedTime || publishedTime,
              author: {
                '@type': 'Organization',
                name: 'GPT-5 Hub',
                url: baseUrl,
              },
            }),
          }}
        />
      </Head>
      
      {/* Additional structured data */}
      {structuredData && <StructuredData data={structuredData} />}
    </>
  );
}

// Specific SEO components for different page types
export function HomePageSEO({ locale }: { locale: string }) {
  const title = locale === 'zh' 
    ? 'GPT-5 中文站 - 最全GPT-5提示词库和AI工具集 | gpt-5.host'
    : 'GPT-5 Hub - Best GPT-5 Prompts, Tools & AI Resources | gpt-5.host';
  
  const description = locale === 'zh'
    ? 'GPT-5中文资源站，提供最全面的GPT-5提示词库、AI工具目录和使用教程。掌握GPT-5写作、编程、设计技巧，提升AI生产力。'
    : 'Discover the ultimate collection of GPT-5 prompts, AI tools, and resources. Master GPT-5 for writing, coding, design, and business automation.';

  return (
    <SEOHead
      title={title}
      description={description}
      url={locale === 'en' ? '/' : '/zh'}
      locale={locale}
      pageType="home"
    />
  );
}

export function PromptsPageSEO({ locale }: { locale: string }) {
  const title = locale === 'zh'
    ? 'GPT-5提示词库 - 最全面的AI提示词集合 | GPT-5 Hub'
    : 'GPT-5 Prompts Library - Complete AI Prompts Collection | GPT-5 Hub';
  
  const description = locale === 'zh'
    ? '浏览最全面的GPT-5提示词库，包含写作、编程、设计、营销等各领域的高质量AI提示词。提升您的GPT-5使用效率。'
    : 'Browse the most comprehensive GPT-5 prompts library with high-quality AI prompts for writing, coding, design, marketing, and more. Boost your GPT-5 productivity.';

  return (
    <SEOHead
      title={title}
      description={description}
      url={locale === 'en' ? '/prompts' : '/zh/prompts'}
      locale={locale}
      pageType="prompts"
    />
  );
}

export function ToolsPageSEO({ locale }: { locale: string }) {
  const title = locale === 'zh'
    ? 'GPT-5工具目录 - 最佳AI工具推荐 | GPT-5 Hub'
    : 'GPT-5 Tools Directory - Best AI Tools Recommendations | GPT-5 Hub';
  
  const description = locale === 'zh'
    ? '发现最佳的GPT-5相关工具和AI应用。精选的生产力工具、写作助手、编程工具等，助您充分发挥GPT-5的潜力。'
    : 'Discover the best GPT-5 related tools and AI applications. Curated productivity tools, writing assistants, coding tools, and more to maximize your GPT-5 potential.';

  return (
    <SEOHead
      title={title}
      description={description}
      url={locale === 'en' ? '/tools' : '/zh/tools'}
      locale={locale}
      pageType="tools"
    />
  );
}
