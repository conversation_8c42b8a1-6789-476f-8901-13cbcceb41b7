import { generateSEOMetadata as generateSEOMeta, getPageKeywords } from '@/lib/seo';
import { StructuredData } from './structured-data';

interface SEOHeadProps {
  title: string;
  description: string;
  url: string;
  locale: string;
  image?: string;
  type?: 'website' | 'article';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  pageType?: 'home' | 'prompts' | 'tools' | 'blog' | 'comparison';
  structuredData?: Record<string, any> | Record<string, any>[];
}

// For App Router, we use generateMetadata instead of Head component
// This component is kept for reference but should use generateMetadata in pages
export function generateSEOMetadata({
  title,
  description,
  url,
  locale,
  image,
  type = 'website',
  author,
  publishedTime,
  modifiedTime,
  pageType = 'home',
}: SEOHeadProps) {
  const keywords = getPageKeywords(pageType, locale);
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://gpt-5.host';
  const fullUrl = `${baseUrl}${url}`;
  const fullImageUrl = image ? (image.startsWith('http') ? image : `${baseUrl}${image}`) : `${baseUrl}/logo.png`;

  return generateSEOMeta({
    title,
    description,
    keywords,
    url,
    image: fullImageUrl,
    locale,
    type,
    author,
    publishedTime,
    modifiedTime,
  });
}

// Helper functions for generating page-specific SEO metadata
export function getHomePageSEO(locale: string) {
  const title = locale === 'zh'
    ? 'GPT-5 中文站 - 最全GPT-5提示词库和AI工具集 | gpt-5.host'
    : 'GPT-5 Hub - Best GPT-5 Prompts, Tools & AI Resources | gpt-5.host';

  const description = locale === 'zh'
    ? 'GPT-5中文资源站，提供最全面的GPT-5提示词库、AI工具目录和使用教程。掌握GPT-5写作、编程、设计技巧，提升AI生产力。'
    : 'Discover the ultimate collection of GPT-5 prompts, AI tools, and resources. Master GPT-5 for writing, coding, design, and business automation.';

  return generateSEOMeta({
    title,
    description,
    keywords: getPageKeywords('home', locale),
    url: locale === 'en' ? '/' : `/${locale}`,
    locale,
    type: 'website',
  });
}

export function getPromptsPageSEO(locale: string) {
  const title = locale === 'zh'
    ? 'GPT-5提示词库 - 最全面的AI提示词集合 | GPT-5 Hub'
    : 'GPT-5 Prompts Library - Complete AI Prompts Collection | GPT-5 Hub';

  const description = locale === 'zh'
    ? '浏览最全面的GPT-5提示词库，包含写作、编程、设计、营销等各领域的高质量AI提示词。提升您的GPT-5使用效率。'
    : 'Browse the most comprehensive GPT-5 prompts library with high-quality AI prompts for writing, coding, design, marketing, and more. Boost your GPT-5 productivity.';

  return generateSEOMeta({
    title,
    description,
    keywords: getPageKeywords('prompts', locale),
    url: locale === 'en' ? '/prompts' : `/${locale}/prompts`,
    locale,
    type: 'website',
  });
}

export function getToolsPageSEO(locale: string) {
  const title = locale === 'zh'
    ? 'GPT-5工具目录 - 最佳AI工具推荐 | GPT-5 Hub'
    : 'GPT-5 Tools Directory - Best AI Tools Recommendations | GPT-5 Hub';

  const description = locale === 'zh'
    ? '发现最佳的GPT-5相关工具和AI应用。精选的生产力工具、写作助手、编程工具等，助您充分发挥GPT-5的潜力。'
    : 'Discover the best GPT-5 related tools and AI applications. Curated productivity tools, writing assistants, coding tools, and more to maximize your GPT-5 potential.';

  return generateSEOMeta({
    title,
    description,
    keywords: getPageKeywords('tools', locale),
    url: locale === 'en' ? '/tools' : `/${locale}/tools`,
    locale,
    type: 'website',
  });
}
