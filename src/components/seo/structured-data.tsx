import Script from 'next/script';

interface StructuredDataProps {
  data: Record<string, any> | Record<string, any>[];
}

export function StructuredData({ data }: StructuredDataProps) {
  const jsonLd = Array.isArray(data) ? data : [data];
  
  return (
    <Script
      id="structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(jsonLd),
      }}
    />
  );
}

// Specific structured data components for different page types
export function OrganizationStructuredData() {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://gpt-5.host';
  
  const organizationData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'GPT-5 Hub',
    alternateName: 'GPT5 Hub',
    url: baseUrl,
    logo: `${baseUrl}/logo.png`,
    description: 'Ultimate resource for GPT-5 prompts, tools, and AI insights. Discover the best ways to leverage GPT-5 for writing, coding, design, and business automation.',
    foundingDate: '2024',
    sameAs: [
      'https://twitter.com/gpt5hub',
      'https://github.com/gpt5hub',
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'customer service',
      url: `${baseUrl}/contact`,
    },
  };

  return <StructuredData data={organizationData} />;
}

export function WebsiteStructuredData() {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://gpt-5.host';
  
  const websiteData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'GPT-5 Hub',
    alternateName: 'GPT5 Hub',
    url: baseUrl,
    description: 'Discover the ultimate collection of GPT-5 prompts, AI tools, and resources. Master GPT-5 for writing, coding, design, and business automation.',
    inLanguage: ['en', 'zh'],
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${baseUrl}/search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
    publisher: {
      '@type': 'Organization',
      name: 'GPT-5 Hub',
      url: baseUrl,
      logo: `${baseUrl}/logo.png`,
    },
  };

  return <StructuredData data={websiteData} />;
}

export function ArticleStructuredData({
  title,
  description,
  url,
  publishedTime,
  modifiedTime,
  author = 'GPT-5 Hub Team',
  image,
}: {
  title: string;
  description: string;
  url: string;
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  image?: string;
}) {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://gpt-5.host';
  const fullUrl = `${baseUrl}${url}`;
  const fullImageUrl = image ? (image.startsWith('http') ? image : `${baseUrl}${image}`) : `${baseUrl}/logo.png`;

  const articleData = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: title,
    description,
    url: fullUrl,
    datePublished: publishedTime,
    dateModified: modifiedTime || publishedTime,
    author: {
      '@type': 'Person',
      name: author,
    },
    publisher: {
      '@type': 'Organization',
      name: 'GPT-5 Hub',
      url: baseUrl,
      logo: {
        '@type': 'ImageObject',
        url: `${baseUrl}/logo.png`,
      },
    },
    image: {
      '@type': 'ImageObject',
      url: fullImageUrl,
      width: 1200,
      height: 630,
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': fullUrl,
    },
    keywords: 'GPT-5, AI, prompts, tools, artificial intelligence, machine learning',
  };

  return <StructuredData data={articleData} />;
}

export function BreadcrumbStructuredData({
  items,
}: {
  items: Array<{ name: string; url: string }>;
}) {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://gpt-5.host';
  
  const breadcrumbData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url.startsWith('http') ? item.url : `${baseUrl}${item.url}`,
    })),
  };

  return <StructuredData data={breadcrumbData} />;
}

export function FAQStructuredData({
  faqs,
}: {
  faqs: Array<{ question: string; answer: string }>;
}) {
  const faqData = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map((faq) => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };

  return <StructuredData data={faqData} />;
}

export function SoftwareApplicationStructuredData() {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://gpt-5.host';
  
  const softwareData = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: 'GPT-5 Hub',
    applicationCategory: 'WebApplication',
    operatingSystem: 'Web Browser',
    description: 'Ultimate resource for GPT-5 prompts, tools, and AI insights. Discover the best ways to leverage GPT-5 for writing, coding, design, and business automation.',
    url: baseUrl,
    screenshot: `${baseUrl}/preview.png`,
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      ratingCount: '1250',
      bestRating: '5',
      worstRating: '1',
    },
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
    },
    publisher: {
      '@type': 'Organization',
      name: 'GPT-5 Hub',
      url: baseUrl,
    },
  };

  return <StructuredData data={softwareData} />;
}
