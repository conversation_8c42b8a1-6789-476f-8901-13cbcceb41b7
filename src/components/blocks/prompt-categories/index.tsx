"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Icon from "@/components/icon";
import { CopyToClipboard } from "react-copy-to-clipboard";
import { toast } from "sonner";

interface Prompt {
  title: string;
  content: string;
  category: string;
}

interface PromptTab {
  id: string;
  title: string;
  description: string;
  icon: string;
  prompts: Prompt[];
}

interface PromptCategoriesSection {
  name: string;
  title: string;
  description: string;
  type: string;
  tabs: PromptTab[];
  disabled?: boolean;
}

export default function PromptCategories({ section }: { section: PromptCategoriesSection }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section className="py-16 bg-muted/30" id="prompts">
      <div className="container px-4">
        <div className="mb-12 text-center">
          <h2 className="mb-4 text-3xl font-bold lg:text-4xl">
            {section.title}
          </h2>
          <p className="mx-auto max-w-2xl text-muted-foreground lg:text-lg">
            {section.description}
          </p>
        </div>
        
        <div className="mx-auto max-w-6xl">
          <Tabs defaultValue={section.tabs[0]?.id} className="w-full">
            <TabsList className="grid w-full grid-cols-2 md:grid-cols-3 lg:grid-cols-6 mb-8 h-auto">
              {section.tabs?.map((tab) => (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  className="flex flex-col items-center gap-2 p-3 h-auto min-h-[60px] data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  <Icon name={tab.icon} className="text-xl" />
                  <div className="text-center">
                    <div className="font-medium text-xs lg:text-sm">{tab.title}</div>
                  </div>
                </TabsTrigger>
              ))}
            </TabsList>
            
            {section.tabs?.map((tab) => (
              <TabsContent key={tab.id} value={tab.id} className="space-y-6">
                {/* Category Description */}
                <div className="text-center py-4 border-b border-border/50">
                  <div className="flex items-center justify-center gap-3 mb-2">
                    <Icon name={tab.icon} className="text-2xl text-primary" />
                    <h3 className="text-xl font-semibold">{tab.title}</h3>
                  </div>
                  <p className="text-muted-foreground max-w-2xl mx-auto">
                    {tab.description}
                  </p>
                </div>

                <div className="grid gap-4">
                  {tab.prompts?.map((prompt, index) => (
                    <Card key={index} className="group hover:shadow-lg transition-all duration-200 border-l-4 border-l-primary/20 hover:border-l-primary">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between gap-4">
                          <div className="flex-1 min-w-0">
                            <CardTitle className="text-lg mb-2 line-clamp-2">{prompt.title}</CardTitle>
                            <Badge variant="secondary" className="text-xs">
                              {prompt.category}
                            </Badge>
                          </div>
                          <CopyToClipboard
                            text={prompt.content}
                            onCopy={() => toast.success("Prompt copied to clipboard!")}
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              className="shrink-0 opacity-70 group-hover:opacity-100 hover:bg-primary hover:text-primary-foreground transition-all"
                            >
                              <Icon name="RiFileCopyLine" className="mr-2 h-4 w-4" />
                              Copy
                            </Button>
                          </CopyToClipboard>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <CardDescription className="text-sm leading-relaxed text-muted-foreground">
                          {prompt.content}
                        </CardDescription>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                
                <div className="text-center pt-8">
                  <Button variant="outline" className="gap-2 hover:bg-primary hover:text-primary-foreground transition-colors">
                    <Icon name="RiArrowRightLine" className="h-4 w-4" />
                    View All {tab.title} Prompts
                  </Button>
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </div>
    </section>
  );
}
