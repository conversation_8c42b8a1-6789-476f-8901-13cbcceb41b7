"use client";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";
import Image from "next/image";
import Link from "next/link";

interface Tool {
  id: string;
  title: string;
  description: string;
  chats: string;
  image: string;
  url: string;
  category?: string;
}

interface ToolsShowcaseSection {
  name: string;
  title: string;
  description: string;
  tools: Tool[];
  disabled?: boolean;
}

export default function ToolsShowcase({ section }: { section: ToolsShowcaseSection }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section className="py-16" id="tools">
      <div className="container px-4">
        <div className="mb-12 text-center">
          <h2 className="mb-4 text-3xl font-bold lg:text-4xl">
            {section.title}
          </h2>
          <p className="mx-auto max-w-2xl text-muted-foreground lg:text-lg">
            {section.description}
          </p>
        </div>
        
        <div className="mx-auto max-w-7xl">
          <div className="grid w-full grid-cols-1 gap-5 md:grid-cols-2 lg:grid-cols-3">
            {section.tools?.map((tool) => (
              <Link 
                key={tool.id} 
                href={tool.url} 
                target="_blank"
                rel="noopener noreferrer"
                className="group"
              >
                <Card className="group relative h-full transition-all duration-300 hover:shadow-lg hover:border-primary/20 border-l-4 border-l-primary/20 hover:border-l-primary bg-card hover:bg-primary/5">
                  <CardContent className="p-4">
                    <div className="flex gap-3 items-start">
                      <div className="min-w-0 flex-1">
                        <h3 className="text-base font-semibold text-foreground transition-colors group-hover:text-primary mb-1 line-clamp-2">
                          {tool.title}
                        </h3>
                        <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                          {tool.description}
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-1">
                            <Icon name="RiChatSmile3Line" className="h-3 w-3 text-muted-foreground" />
                            <span className="font-mono text-xs text-muted-foreground">
                              {tool.chats}
                            </span>
                          </div>
                          {tool.category && (
                            <Badge variant="secondary" className="text-xs">
                              {tool.category}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="shrink-0">
                        <div className="relative h-16 w-16 overflow-hidden rounded-full border-2 border-border group-hover:border-primary/50 transition-all duration-300 group-hover:scale-105">
                          <Image
                            src={tool.image}
                            alt={tool.title}
                            fill
                            className="object-cover"
                            sizes="64px"
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
          
          <div className="text-center pt-12">
            <Button variant="outline" className="gap-2 hover:bg-primary hover:text-primary-foreground transition-colors">
              <Icon name="RiAppsLine" className="h-4 w-4" />
              View All Tools
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
